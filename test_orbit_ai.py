#!/usr/bin/env python3
"""
Test script for Orbit AI Chat Assistant

This script tests the Perplexity integration and chat functionality.
Run this from the backend directory.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def test_perplexity_client():
    """Test the Perplexity client directly."""
    print("🧪 Testing Perplexity Client...")
    
    try:
        from app.services.perplexity.client import PerplexityClient
        
        # Check if API key is set
        api_key = os.getenv("PERPLEXITY_API_KEY")
        if not api_key:
            print("❌ PERPLEXITY_API_KEY not set in environment")
            return False
        
        client = PerplexityClient(api_key)
        
        # Test messages
        messages = [
            {"role": "system", "content": "You are a helpful investment analyst."},
            {"role": "user", "content": "What are the key factors to consider when evaluating a SaaS startup?"}
        ]
        
        print("📡 Sending test message to Perplexity...")
        completion_data = await client.create_chat_completion(messages)
        
        # Extract content and sources
        content = client.extract_content_from_completion(completion_data)
        sources = client.extract_sources_from_completion(completion_data)
        
        print(f"✅ Received response: {content[:100]}...")
        print(f"📚 Found {len(sources)} sources")
        
        for i, source in enumerate(sources[:3]):  # Show first 3 sources
            print(f"   {i+1}. {source.title} - {source.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Perplexity test failed: {e}")
        return False

async def test_chat_service():
    """Test the chat service."""
    print("\n🧪 Testing Chat Service...")
    
    try:
        from app.services.chat.service import ChatService
        from app.schemas.chat import SendMessageRequest
        from motor.motor_asyncio import AsyncIOMotorClient
        
        # Mock database connection
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.test_tractionx
        
        service = ChatService(db)
        
        # Test thread creation
        print("📝 Creating test thread...")
        thread = await service.get_or_create_thread(
            deal_id="507f1f77bcf86cd799439011",  # Mock ObjectId
            user_id="507f1f77bcf86cd799439012",  # Mock ObjectId
            org_id="507f1f77bcf86cd799439013"   # Mock ObjectId
        )
        
        print(f"✅ Thread created: {thread.id}")
        
        # Test message sending (this will call Perplexity)
        print("💬 Sending test message...")
        request = SendMessageRequest(
            message="What makes a good investment opportunity?",
            include_deal_context=False
        )
        
        ai_message = await service.send_message(
            thread_id=str(thread.id),
            user_id="507f1f77bcf86cd799439012",
            request=request
        )
        
        print(f"✅ AI response received: {ai_message.content[:100]}...")
        print(f"📚 Sources: {len(ai_message.sources)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chat service test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Orbit AI Tests\n")
    
    # Test 1: Perplexity Client
    perplexity_ok = await test_perplexity_client()
    
    # Test 2: Chat Service (only if Perplexity works)
    chat_ok = False
    if perplexity_ok:
        chat_ok = await test_chat_service()
    
    # Summary
    print("\n📊 Test Results:")
    print(f"   Perplexity Client: {'✅ PASS' if perplexity_ok else '❌ FAIL'}")
    print(f"   Chat Service: {'✅ PASS' if chat_ok else '❌ FAIL'}")
    
    if perplexity_ok and chat_ok:
        print("\n🎉 All tests passed! Orbit AI is ready to go.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        
        if not perplexity_ok:
            print("\n💡 To fix Perplexity issues:")
            print("   1. Set PERPLEXITY_API_KEY in your environment")
            print("   2. Ensure you have a valid Perplexity account")
            print("   3. Check your internet connection")

if __name__ == "__main__":
    asyncio.run(main())
