"""
Chat Service Implementation

Handles chat operations including thread management, message processing,
and integration with Perplexity AI for async completions.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.jobs.chat_completion import queue_chat_completion
from app.models.chat import (
    ChatCompletionJob,
    ChatMessage,
    ChatSource,
    ChatThread,
    MessageRole,
    MessageStatus,
)
from app.schemas.chat import SendMessageRequest
from app.services.base import BaseService
from app.services.chat.interface import IChatService

logger = get_logger(__name__)


class ChatService(BaseService, IChatService):
    """Implementation of chat service operations."""

    def __init__(self, db: AsyncIOMotorDatabase):
        super().__init__()
        self.db = db
        from app.services.perplexity.client import PerplexityClient

        self.perplexity_client = PerplexityClient()

    async def initialize(self) -> None:
        """Initialize service resources."""
        # Initialize any resources needed by the chat service
        # The database and perplexity client are already initialized in __init__
        self.logger.info("Chat service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        # Clean up any resources used by the chat service
        # Currently no cleanup needed for database or perplexity client
        self.logger.info("Chat service cleaned up")

    async def get_or_create_thread(
        self, deal_id: str, user_id: str, org_id: str
    ) -> ChatThread:
        """Get existing thread or create new one for deal/user combination."""
        try:
            # Try to find existing thread
            existing_thread = await ChatThread.find_one({
                "deal_id": ObjectId(deal_id),
                "user_id": ObjectId(user_id),
                "org_id": ObjectId(org_id),
            })

            if existing_thread:
                logger.info(f"Found existing chat thread: {existing_thread.id}")
                return existing_thread

            # Create new thread
            thread = ChatThread(
                deal_id=ObjectId(deal_id),
                user_id=ObjectId(user_id),
                org_id=ObjectId(org_id),
                title=f"Chat with {deal_id}",
            )

            await thread.save()
            logger.info(f"Created new chat thread: {thread.id}")
            return thread

        except Exception as e:
            logger.error(f"Error getting/creating thread: {e}")
            raise

    async def get_thread_messages(
        self, thread_id: str, limit: int = 50, skip: int = 0
    ) -> List[ChatMessage]:
        """Get messages for a thread with pagination."""
        try:
            messages = (
                await ChatMessage.find({"thread_id": ObjectId(thread_id)})
                .sort([("created_at", 1)])
                .skip(skip)
                .limit(limit)
                .to_list()
            )

            return messages

        except Exception as e:
            logger.error(f"Error getting thread messages: {e}")
            raise

    async def send_message(
        self,
        thread_id: str,
        user_id: str,
        request: SendMessageRequest,
        deal_context: Optional[Dict[str, Any]] = None,
    ) -> ChatMessage:
        """Send a new message and trigger AI completion."""
        try:
            # Create user message
            user_message = ChatMessage(
                thread_id=ObjectId(thread_id),
                role=MessageRole.USER,
                content=request.message,
                deal_context=deal_context,
            )
            await user_message.save()

            # Get chat history for context
            chat_history = await self._get_chat_history_for_context(thread_id)

            # Build prompt with context
            messages = self.perplexity_client.build_investment_prompt(
                user_message=request.message,
                deal_context=deal_context,
                chat_history=chat_history,
            )

            # Create async completion request
            perplexity_request_id = (
                await self.perplexity_client.create_async_completion(
                    messages=messages,
                    model="sonar-reasoning",
                    max_tokens=1000,
                    temperature=0.2,
                )
            )

            # Create AI response message (pending)
            ai_message = ChatMessage(
                thread_id=ObjectId(thread_id),
                role=MessageRole.ASSISTANT,
                content="",  # Will be filled when completion is ready
                status=MessageStatus.PENDING,
                perplexity_request_id=perplexity_request_id,
                perplexity_model="sonar",
                deal_context=deal_context,
            )
            await ai_message.save()

            # Create completion job for background processing
            completion_job = ChatCompletionJob(
                message_id=ai_message.id,
                thread_id=ObjectId(thread_id),
                perplexity_request_id=perplexity_request_id,
            )
            await completion_job.save()

            # Queue background job to poll for completion
            job_id = await queue_chat_completion(str(completion_job.id))

            # Update job with RQ job ID
            completion_job.job_id = job_id
            await completion_job.save()

            # Update thread metadata
            await self._update_thread_metadata(thread_id)

            logger.info(f"Sent message and queued completion: {ai_message.id}")
            return ai_message

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise

    async def get_message_status(
        self, message_id: str
    ) -> Tuple[MessageStatus, Optional[str]]:
        """Get the current status of a message."""
        try:
            message = await ChatMessage.find_one({"_id": ObjectId(message_id)})
            if not message:
                return MessageStatus.FAILED, "Message not found"

            return message.status, message.error_message

        except Exception as e:
            logger.error(f"Error getting message status: {e}")
            return MessageStatus.FAILED, str(e)

    async def update_message_completion(
        self,
        message_id: str,
        content: str,
        sources: List[Dict[str, Any]],
        status: MessageStatus = MessageStatus.COMPLETED,
    ) -> ChatMessage:
        """Update message with completion results."""
        try:
            # Convert sources to ChatSource objects
            chat_sources = []
            for source_data in sources:
                chat_source = ChatSource(**source_data)
                chat_sources.append(chat_source)

            # Update message
            message = await ChatMessage.find_one({"_id": ObjectId(message_id)})
            if not message:
                raise ValueError(f"Message {message_id} not found")

            message.content = content
            message.sources = chat_sources
            message.status = status
            message.updated_at = int(datetime.now(timezone.utc).timestamp())

            await message.save()

            # Update thread metadata
            await self._update_thread_metadata(str(message.thread_id))

            logger.info(f"Updated message completion: {message_id}")
            return message

        except Exception as e:
            logger.error(f"Error updating message completion: {e}")
            raise

    async def mark_message_failed(
        self, message_id: str, error_message: str
    ) -> ChatMessage:
        """Mark message as failed with error details."""
        try:
            message = await ChatMessage.find_one({"_id": ObjectId(message_id)})
            if not message:
                raise ValueError(f"Message {message_id} not found")

            message.status = MessageStatus.FAILED
            message.error_message = error_message
            message.updated_at = int(datetime.now(timezone.utc).timestamp())

            await message.save()

            logger.warning(f"Marked message as failed: {message_id} - {error_message}")
            return message

        except Exception as e:
            logger.error(f"Error marking message as failed: {e}")
            raise

    async def get_chat_stats(self, user_id: str, org_id: str) -> Dict[str, Any]:
        """Get chat statistics for a user."""
        try:
            # Count threads
            total_threads = await ChatThread.count_documents({
                "user_id": ObjectId(user_id),
                "org_id": ObjectId(org_id),
            })

            # Count messages
            thread_ids = await ChatThread.find({
                "user_id": ObjectId(user_id),
                "org_id": ObjectId(org_id),
            }).distinct("_id")

            total_messages = await ChatMessage.count_documents({
                "thread_id": {"$in": thread_ids}
            })

            # Count active threads (with messages in last 7 days)
            week_ago = int((datetime.now(timezone.utc).timestamp() - 7 * 24 * 3600))
            active_threads = await ChatThread.count_documents({
                "user_id": ObjectId(user_id),
                "org_id": ObjectId(org_id),
                "last_message_at": {"$gte": week_ago},
            })

            return {
                "total_threads": total_threads,
                "total_messages": total_messages,
                "active_threads": active_threads,
                "avg_response_time": None,  # TODO: Calculate from completion jobs
            }

        except Exception as e:
            logger.error(f"Error getting chat stats: {e}")
            raise

    async def _get_chat_history_for_context(
        self, thread_id: str
    ) -> List[Dict[str, str]]:
        """Get recent chat history for context."""
        try:
            messages = (
                await ChatMessage.find({
                    "thread_id": ObjectId(thread_id),
                    "status": MessageStatus.COMPLETED,
                })
                .sort([("created_at", -1)])
                .limit(10)
                .to_list()
            )

            # Reverse to get chronological order
            messages.reverse()

            history = []
            for msg in messages:
                history.append({"role": msg.role.value, "content": msg.content})

            return history

        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []

    async def _update_thread_metadata(self, thread_id: str):
        """Update thread metadata after new messages."""
        try:
            thread = await ChatThread.find_one({"_id": ObjectId(thread_id)})
            if not thread:
                return

            # Count messages
            message_count = await ChatMessage.count_documents({
                "thread_id": ObjectId(thread_id)
            })

            # Update thread
            thread.message_count = message_count
            thread.last_message_at = int(datetime.now(timezone.utc).timestamp())
            thread.updated_at = int(datetime.now(timezone.utc).timestamp())

            await thread.save()

        except Exception as e:
            logger.error(f"Error updating thread metadata: {e}")
