"""
Perplexity API Client

Handles async chat completions with Perplexity Sonar API.
"""

from typing import Any, Dict, List, Optional, Tuple

import httpx

from app.core.config import settings
from app.core.logging import get_logger
from app.models.chat import ChatSource

logger = get_logger(__name__)


class PerplexityError(Exception):
    """Base exception for Perplexity API errors."""

    pass


class PerplexityRateLimitError(PerplexityError):
    """Raised when rate limit is exceeded."""

    pass


class PerplexityTimeoutError(PerplexityError):
    """Raised when request times out."""

    pass


class PerplexityClient:
    """
    Async client for Perplexity Sonar API.
    Handles chat completions with sources and citations.
    """

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or getattr(settings, "PERPLEXITY_API_KEY", None)
        if not self.api_key:
            raise ValueError("Perplexity API key is required")

        self.base_url = "https://api.perplexity.ai"
        self.timeout = 30.0

        # Default headers
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    async def create_async_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "sonar-reasoning",
        max_tokens: int = 1000,
        temperature: float = 0.2,
        search_mode: str = "web",
        reasoning_effort: str = "medium",
    ) -> str:
        """
        Create an async chat completion request.

        Returns:
            request_id: Perplexity request ID for polling
        """
        payload = {
            "request": {
                "model": model,
                "messages": messages,
                "search_mode": search_mode,
                "reasoning_effort": reasoning_effort,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 0.9,
                "search_domain_filter": ["<any>"],
                "return_images": False,
                "return_related_questions": False,
                "top_k": 0,
                "stream": False,
                "presence_penalty": 0,
                "frequency_penalty": 0,
                "web_search_options": {"search_context_size": "high"},
            }
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.base_url}/async/chat/completions",
                    headers=self.headers,
                    json=payload,
                )

                if response.status_code == 429:
                    raise PerplexityRateLimitError("Rate limit exceeded")
                elif response.status_code >= 400:
                    error_detail = response.text
                    raise PerplexityError(
                        f"API error {response.status_code}: {error_detail}"
                    )

                result = response.json()
                request_id = result.get("id")

                if not request_id:
                    raise PerplexityError("No request ID returned from Perplexity API")

                logger.info(f"Created Perplexity async completion: {request_id}")
                return request_id

        except httpx.TimeoutException:
            raise PerplexityTimeoutError("Request timed out")
        except Exception as e:
            logger.error(f"Error creating async completion: {e}")
            raise PerplexityError(f"Failed to create completion: {str(e)}")

    async def get_completion_status(
        self, request_id: str
    ) -> Tuple[str, Optional[Dict[str, Any]]]:
        """
        Get the status of an async completion request.

        Returns:
            Tuple of (status, result_data)
            status: "pending", "completed", "failed"
            result_data: Completion data if completed, None otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.base_url}/async/chat/completions/{request_id}",
                    headers=self.headers,
                )

                if response.status_code == 404:
                    return "failed", None
                elif response.status_code >= 400:
                    error_detail = response.text
                    logger.error(f"Error getting completion status: {error_detail}")
                    return "failed", None

                result = response.json()
                status = result.get("status", "pending")

                if status == "completed":
                    # Extract completion data
                    completion_data = result.get("result", {})
                    return "completed", completion_data
                elif status == "failed":
                    error_msg = result.get("error", "Unknown error")
                    logger.error(f"Perplexity completion failed: {error_msg}")
                    return "failed", {"error": error_msg}
                else:
                    # Still pending
                    return "pending", None

        except httpx.TimeoutException:
            logger.warning(f"Timeout checking completion status for {request_id}")
            return "pending", None
        except Exception as e:
            logger.error(f"Error checking completion status: {e}")
            return "failed", {"error": str(e)}

    def extract_sources_from_completion(
        self, completion_data: Dict[str, Any]
    ) -> List[ChatSource]:
        """
        Extract sources/citations from Perplexity completion response.
        """
        sources = []

        try:
            # Perplexity returns citations in the response
            citations = completion_data.get("citations", [])

            for citation in citations:
                source = ChatSource(
                    title=citation.get("title", "Unknown Source"),
                    url=citation.get("url", ""),
                    snippet=citation.get("snippet"),
                    domain=citation.get("domain"),
                )
                sources.append(source)

        except Exception as e:
            logger.error(f"Error extracting sources: {e}")

        return sources

    def build_investment_prompt(
        self,
        user_message: str,
        deal_context: Optional[Dict[str, Any]] = None,
        chat_history: Optional[List[Dict[str, str]]] = None,
    ) -> List[Dict[str, str]]:
        """
        Build a context-aware prompt for investment analysis.
        """
        messages = []

        # System message with investment context
        system_content = """You are Orbit, an AI investment analyst for TractionX. You provide precise, data-driven investment analysis with sources and citations.

Key guidelines:
- Always supplement answers with credible sources and citations
- Focus on investment-relevant insights (market size, competition, traction, team)
- Be concise but thorough in your analysis
- If asked about a specific deal, use only the provided context
- Never speculate without data - clearly state when information is limited"""

        # Add deal context if provided
        if deal_context:
            company_name = deal_context.get("company_name", "this company")
            sector = deal_context.get("sector", "")
            stage = deal_context.get("stage", "")

            system_content += "\n\nCurrent deal context:\n"
            system_content += f"- Company: {company_name}\n"
            if sector:
                system_content += f"- Sector: {sector}\n"
            if stage:
                system_content += f"- Stage: {stage}\n"

        messages.append({"role": "system", "content": system_content})

        # Add chat history if provided
        if chat_history:
            messages.extend(chat_history[-10:])  # Last 10 messages for context

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        return messages
