"""
Chat API Endpoints

REST API endpoints for Orbit AI Investment Chat Assistant.
Supports thread management, message sending, and status polling.
"""

from bson import ObjectId
from fastapi import Depends, HTTPException, Query, status

from app.api.base import BaseAPIRouter
from app.core.database import get_database
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.deal import Deal
from app.models.user import User
from app.schemas.chat import (
    ChatHistoryResponse,
    ChatMessageResponse,
    ChatSourceResponse,
    ChatStatsResponse,
    ChatThreadResponse,
    MessageStatusResponse,
    SendMessageRequest,
)
from app.services.chat.interface import IChatService
from app.services.factory import get_chat_service, get_deal_service
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)

router = BaseAPIRouter(prefix="/deals", tags=["chat"])


def _message_to_response(message) -> ChatMessageResponse:
    """Convert ChatMessage model to response schema."""
    sources = [
        ChatSourceResponse(
            title=source.title,
            url=source.url,
            snippet=source.snippet,
            domain=source.domain,
        )
        for source in message.sources
    ]

    return ChatMessageResponse(
        id=str(message.id),
        thread_id=str(message.thread_id),
        role=message.role,
        content=message.content,
        status=message.status,
        sources=sources,
        error_message=message.error_message,
        created_at=message.created_at,
        updated_at=message.updated_at,
    )


def _thread_to_response(thread, messages) -> ChatThreadResponse:
    """Convert ChatThread model to response schema."""
    message_responses = [_message_to_response(msg) for msg in messages]

    return ChatThreadResponse(
        id=str(thread.id),
        deal_id=str(thread.deal_id),
        user_id=str(thread.user_id),
        title=thread.title,
        message_count=thread.message_count,
        last_message_at=thread.last_message_at,
        messages=message_responses,
        created_at=thread.created_at,
        updated_at=thread.updated_at,
    )


@router.get("/{deal_id}/chat", response_model=ChatHistoryResponse)
@rbac_register(
    resource="chat", action="view", group="Chat", description="View chat history"
)
async def get_chat_history(
    deal_id: str,
    limit: int = Query(
        default=50, ge=1, le=100, description="Number of messages to return"
    ),
    skip: int = Query(default=0, ge=0, description="Number of messages to skip"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db=Depends(get_database),
    deal_service=Depends(get_deal_service),
):
    """
    Get chat history for a deal.

    Returns the chat thread and messages for the current user and deal.
    Creates a new thread if one doesn't exist.
    """
    try:
        org_id, _ = org_context

        # Verify deal exists and user has access
        deal = await Deal.find_one(query={"_id": ObjectId(deal_id)})
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

        # Initialize chat service
        chat_service: IChatService = await get_chat_service()  # type: ignore

        # Get or create thread
        thread = await chat_service.get_or_create_thread(
            deal_id=deal_id, user_id=str(current_user.id), org_id=org_id
        )

        # Get messages
        messages = await chat_service.get_thread_messages(
            thread_id=str(thread.id), limit=limit, skip=skip
        )

        # Build response
        thread_response = _thread_to_response(thread, messages)

        return ChatHistoryResponse(
            thread=thread_response,
            has_more=len(messages) == limit,
            total_messages=thread.message_count,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/{deal_id}/chat", response_model=ChatMessageResponse)
@rbac_register(
    resource="chat", action="create", group="Chat", description="Send chat message"
)
async def send_chat_message(
    deal_id: str,
    request: SendMessageRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
):
    """
    Send a new chat message.

    Creates a user message and triggers async AI completion.
    Returns immediately with pending AI message ID for status polling.
    """
    try:
        org_id, _ = org_context

        # Verify deal exists and user has access
        deal = await Deal.find_one(query={"_id": deal_id})  # type: ignore
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deal not found"
            )

            # Initialize chat service
        chat_service: IChatService = await get_chat_service()  # type: ignore

        # Get or create thread
        thread = await chat_service.get_or_create_thread(
            deal_id=deal_id, user_id=str(current_user.id), org_id=org_id
        )

        # Prepare deal context if requested
        deal_context = None
        if request.include_deal_context:
            deal_context = {
                "company_name": deal.company_name,
                "sector": deal.sector,
                "stage": deal.stage,
                "website": deal.company_website,
                "status": deal.status.value if deal.status else None,
                "scoring": deal.scoring,
                "founders": deal.founders,
            }

        # Send message and get immediate AI completion
        ai_message = await chat_service.send_message(
            thread_id=str(thread.id),
            user_id=str(current_user.id),
            request=request,
            deal_context=deal_context,
        )

        # Return the completed AI message
        return _message_to_response(ai_message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending chat message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{deal_id}/chat/{message_id}/status", response_model=MessageStatusResponse)
@rbac_register(
    resource="chat", action="view", group="Chat", description="Check message status"
)
async def get_message_status(
    deal_id: str,
    message_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db=Depends(get_database),
):
    """
    Get the status of a chat message.

    Used for polling async completion status.
    Returns current status and content if completed.
    """
    try:
        org_id, _ = org_context

        # Initialize chat service
        chat_service: IChatService = await get_chat_service()  # type: ignore

        # Get message status
        status_result, error_message = await chat_service.get_message_status(message_id)

        # Get full message details if needed
        from app.models.chat import ChatMessage

        message = await ChatMessage.find_one({"_id": ObjectId(message_id)})

        if not message:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Message not found"
            )

        # Verify user has access to this message's thread
        from app.models.chat import ChatThread

        thread = await ChatThread.find_one({"_id": message.thread_id})

        if not thread or str(thread.user_id) != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Access denied"
            )

        # Build sources response
        sources = [
            ChatSourceResponse(
                title=source.title,
                url=source.url,
                snippet=source.snippet,
                domain=source.domain,
            )
            for source in message.sources
        ]

        return MessageStatusResponse(
            message_id=message_id,
            status=message.status,
            content=message.content if message.status.value == "completed" else None,
            sources=sources if message.status.value == "completed" else [],
            error_message=message.error_message,
            retry_count=message.retry_count,
            updated_at=message.updated_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting message status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/chat/stats", response_model=ChatStatsResponse)
@rbac_register(
    resource="chat", action="view", group="Chat", description="View chat statistics"
)
async def get_chat_stats(
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db=Depends(get_database),
):
    """
    Get chat statistics for the current user.

    Returns summary statistics about chat usage.
    """
    try:
        org_id, _ = org_context

        # Initialize chat service
        chat_service: IChatService = await get_chat_service()  # type: ignore

        # Get stats
        stats = await chat_service.get_chat_stats(
            user_id=str(current_user.id), org_id=org_id
        )

        return ChatStatsResponse(**stats)

    except Exception as e:
        logger.error(f"Error getting chat stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
